import { useAuth } from "@/context/AuthContext";
import { hasPermission, Permission } from "@/lib/permissions";
import { useEffect, useState } from "react";

export const usePermissions = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user !== undefined) {
      setLoading(false);
    }
  }, [user]);

  const checkPermission = (permission: Permission): boolean => {
    if (!user) return false;
    return hasPermission(user.role, permission);
  };

  // Specific permission checks
  const canManageUsers = () => checkPermission(Permission.EDIT_USERS);
  const canViewUsers = () => checkPermission(Permission.VIEW_USERS);
  const canApproveUsers = () => checkPermission(Permission.APPROVE_USERS);
  
  const canViewFinancials = () => checkPermission(Permission.VIEW_FINANCIAL_REPORTS);
  const canProcessPayments = () => checkPermission(Permission.PROCESS_PAYMENTS);
  
  const canManageInventory = () => checkPermission(Permission.EDIT_INVENTORY);
  const canViewInventory = () => checkPermission(Permission.VIEW_INVENTORY);
  
  const canManageProducts = () => checkPermission(Permission.ADD_PRODUCTS);
  const canViewProducts = () => checkPermission(Permission.VIEW_INVENTORY);
  
  const canManageRecipes = () => checkPermission(Permission.EDIT_RECIPES);
  const canViewRecipes = () => checkPermission(Permission.VIEW_RECIPES);
  
  const canManageOrders = () => checkPermission(Permission.EDIT_ORDERS);
  const canViewOrders = () => checkPermission(Permission.VIEW_ORDERS);
  
  const canManageQRCodes = () => checkPermission(Permission.MANAGE_QR_CODES);
  const canViewQRCodes = () => checkPermission(Permission.VIEW_QR_CODES);
  
  const canManageBars = () => checkPermission(Permission.MANAGE_BARS);
  const canViewBars = () => checkPermission(Permission.VIEW_BARS);
  
  const canViewAnalytics = () => checkPermission(Permission.VIEW_ANALYTICS);
  
  const canManageSettings = () => checkPermission(Permission.MANAGE_SETTINGS);

  // Role-based checks
  const isMaster = () => user?.role === 'master';
  const isAdmin = () => user?.role === 'admin';
  const isManager = () => user?.role === 'manager';
  const isBarman = () => user?.role === 'barman';
  
  const isStaff = () => ['master', 'admin', 'manager', 'barman'].includes(user?.role || '');
  const isHighLevel = () => ['master', 'admin'].includes(user?.role || '');

  return {
    loading,
    user,
    checkPermission,
    
    // User management
    canManageUsers,
    canViewUsers,
    canApproveUsers,
    
    // Financial
    canViewFinancials,
    canProcessPayments,
    
    // Inventory
    canManageInventory,
    canViewInventory,
    
    // Products
    canManageProducts,
    canViewProducts,
    
    // Recipes
    canManageRecipes,
    canViewRecipes,
    
    // Orders
    canManageOrders,
    canViewOrders,
    
    // QR Codes
    canManageQRCodes,
    canViewQRCodes,
    
    // Bars
    canManageBars,
    canViewBars,
    
    // Analytics
    canViewAnalytics,
    
    // Settings
    canManageSettings,
    
    // Role checks
    isMaster,
    isAdmin,
    isManager,
    isBarman,
    isStaff,
    isHighLevel
  };
};
