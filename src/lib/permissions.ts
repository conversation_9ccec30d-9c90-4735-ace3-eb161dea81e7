import { UserRole } from "@/context/AuthContext";

export enum Permission {
  // User Management
  VIEW_USERS = "view_users",
  CREATE_USERS = "create_users",
  EDIT_USERS = "edit_users",
  DELETE_USERS = "delete_users",
  APPROVE_USERS = "approve_users",
  ASSIGN_ROLES = "assign_roles",

  // Analytics & Reports
  VIEW_ANALYTICS = "view_analytics",
  VIEW_FINANCIAL_REPORTS = "view_financial_reports",
  EXPORT_DATA = "export_data",

  // Payment Management
  VIEW_PAYMENTS = "view_payments",
  PROCESS_PAYMENTS = "process_payments",
  MANAGE_PAYMENT_METHODS = "manage_payment_methods",

  // Inventory Management
  VIEW_INVENTORY = "view_inventory",
  EDIT_INVENTORY = "edit_inventory",
  ADD_PRODUCTS = "add_products",
  REMOVE_PRODUCTS = "remove_products",
  MANAGE_SUPPLIERS = "manage_suppliers",

  // Order Management
  VIEW_ORDERS = "view_orders",
  CREATE_ORDERS = "create_orders",
  EDIT_ORDERS = "edit_orders",
  CANCEL_ORDERS = "cancel_orders",
  MANAGE_ORDER_STATUS = "manage_order_status",

  // Recipe Management
  VIEW_RECIPES = "view_recipes",
  CREATE_RECIPES = "create_recipes",
  EDIT_RECIPES = "edit_recipes",
  DELETE_RECIPES = "delete_recipes",

  // System Settings
  MANAGE_SETTINGS = "manage_settings",
  MANAGE_INTEGRATIONS = "manage_integrations",
  VIEW_SYSTEM_LOGS = "view_system_logs",

  // Bar Management
  VIEW_BARS = "view_bars",
  MANAGE_BARS = "manage_bars",
  ASSIGN_STAFF = "assign_staff",

  // QR Code Management
  VIEW_QR_CODES = "view_qr_codes",
  MANAGE_QR_CODES = "manage_qr_codes",
}

// Define permissions for each role
const rolePermissions: Record<UserRole, Permission[]> = {
  master: [
    // Full access to everything
    Permission.VIEW_USERS,
    Permission.CREATE_USERS,
    Permission.EDIT_USERS,
    Permission.DELETE_USERS,
    Permission.APPROVE_USERS,
    Permission.ASSIGN_ROLES,
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_FINANCIAL_REPORTS,
    Permission.EXPORT_DATA,
    Permission.VIEW_PAYMENTS,
    Permission.PROCESS_PAYMENTS,
    Permission.MANAGE_PAYMENT_METHODS,
    Permission.VIEW_INVENTORY,
    Permission.EDIT_INVENTORY,
    Permission.ADD_PRODUCTS,
    Permission.REMOVE_PRODUCTS,
    Permission.MANAGE_SUPPLIERS,
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.EDIT_ORDERS,
    Permission.CANCEL_ORDERS,
    Permission.MANAGE_ORDER_STATUS,
    Permission.VIEW_RECIPES,
    Permission.CREATE_RECIPES,
    Permission.EDIT_RECIPES,
    Permission.DELETE_RECIPES,
    Permission.MANAGE_SETTINGS,
    Permission.MANAGE_INTEGRATIONS,
    Permission.VIEW_SYSTEM_LOGS,
    Permission.VIEW_BARS,
    Permission.MANAGE_BARS,
    Permission.ASSIGN_STAFF,
    Permission.VIEW_QR_CODES,
    Permission.MANAGE_QR_CODES,
  ],

  admin: [
    // High access - most things except system settings and role assignment
    Permission.VIEW_USERS,
    Permission.EDIT_USERS,
    Permission.APPROVE_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_FINANCIAL_REPORTS,
    Permission.EXPORT_DATA,
    Permission.VIEW_PAYMENTS,
    Permission.PROCESS_PAYMENTS,
    Permission.VIEW_INVENTORY,
    Permission.EDIT_INVENTORY,
    Permission.ADD_PRODUCTS,
    Permission.REMOVE_PRODUCTS,
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.EDIT_ORDERS,
    Permission.CANCEL_ORDERS,
    Permission.MANAGE_ORDER_STATUS,
    Permission.VIEW_RECIPES,
    Permission.CREATE_RECIPES,
    Permission.EDIT_RECIPES,
    Permission.DELETE_RECIPES,
    Permission.VIEW_BARS,
    Permission.MANAGE_BARS,
    Permission.ASSIGN_STAFF,
    Permission.VIEW_QR_CODES,
    Permission.MANAGE_QR_CODES,
  ],

  manager: [
    // Day-to-day operations management
    Permission.VIEW_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_INVENTORY,
    Permission.EDIT_INVENTORY,
    Permission.VIEW_ORDERS,
    Permission.CREATE_ORDERS,
    Permission.EDIT_ORDERS,
    Permission.CANCEL_ORDERS,
    Permission.MANAGE_ORDER_STATUS,
    Permission.VIEW_RECIPES,
    Permission.VIEW_BARS,
    Permission.ASSIGN_STAFF,
    Permission.VIEW_QR_CODES,
  ],

  barman: [
    // Limited access - only what's needed for bartending
    Permission.VIEW_ORDERS,
    Permission.MANAGE_ORDER_STATUS,
    Permission.VIEW_RECIPES,
    Permission.VIEW_INVENTORY, // Can see what's available
  ],
};

export function getRolePermissions(role: UserRole): Permission[] {
  return rolePermissions[role] || [];
}

export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  const permissions = getRolePermissions(userRole);
  return permissions.includes(permission);
}

export function canAccessRoute(userRole: UserRole, route: string): boolean {
  const routePermissions: Record<string, Permission[]> = {
    "/dashboard": [Permission.VIEW_ANALYTICS],
    "/users": [Permission.VIEW_USERS],
    "/roles": [Permission.VIEW_USERS, Permission.ASSIGN_ROLES],
    "/inventory": [Permission.VIEW_INVENTORY],
    "/stock": [Permission.VIEW_INVENTORY],
    "/orders": [Permission.VIEW_ORDERS],
    "/bars": [Permission.VIEW_BARS],
    "/qr-codes": [Permission.VIEW_QR_CODES],
    "/finances": [Permission.VIEW_FINANCIAL_REPORTS],
    "/settings": [Permission.MANAGE_SETTINGS],
  };

  const requiredPermissions = routePermissions[route];
  if (!requiredPermissions) return true; // Allow access to routes without specific permissions

  const userPermissions = getRolePermissions(userRole);
  return requiredPermissions.some(permission => userPermissions.includes(permission));
}
